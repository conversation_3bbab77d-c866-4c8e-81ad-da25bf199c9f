<?php
// admin/add_siswa.php - Tambah siswa baru oleh admin

include '../includes/header.php';

// Fungsi untuk resize image
function resizeImage($source, $destination, $max_width, $max_height) {
    $info = getimagesize($source);
    if (!$info) return false;

    $width = $info[0];
    $height = $info[1];
    $type = $info[2];

    // Hitung dimensi baru
    $ratio = min($max_width / $width, $max_height / $height);
    $new_width = round($width * $ratio);
    $new_height = round($height * $ratio);

    // Buat image resource berdasarkan tipe
    switch ($type) {
        case IMAGETYPE_JPEG:
            $source_image = imagecreatefromjpeg($source);
            break;
        case IMAGETYPE_PNG:
            $source_image = imagecreatefrompng($source);
            break;
        case IMAGETYPE_GIF:
            $source_image = imagecreatefromgif($source);
            break;
        default:
            return false;
    }

    // Buat image baru dengan dimensi yang sudah dihitung
    $new_image = imagecreatetruecolor($new_width, $new_height);

    // Preserve transparency untuk PNG dan GIF
    if ($type == IMAGETYPE_PNG || $type == IMAGETYPE_GIF) {
        imagealphablending($new_image, false);
        imagesavealpha($new_image, true);
        $transparent = imagecolorallocatealpha($new_image, 255, 255, 255, 127);
        imagefilledrectangle($new_image, 0, 0, $new_width, $new_height, $transparent);
    }

    // Resize image
    imagecopyresampled($new_image, $source_image, 0, 0, 0, 0, $new_width, $new_height, $width, $height);

    // Simpan image
    switch ($type) {
        case IMAGETYPE_JPEG:
            imagejpeg($new_image, $destination, 85);
            break;
        case IMAGETYPE_PNG:
            imagepng($new_image, $destination);
            break;
        case IMAGETYPE_GIF:
            imagegif($new_image, $destination);
            break;
    }

    // Bersihkan memory
    imagedestroy($source_image);
    imagedestroy($new_image);

    return true;
}

// Cek apakah user adalah admin
if ($_SESSION['role'] !== 'admin') {
    die('<div class="alert alert-danger">Akses ditolak. Halaman ini hanya untuk administrator.</div>');
}

$id_kelas = $_GET['kelas'] ?? 0;
$id_tahun = $_GET['tahun'] ?? 0;

// Get info kelas jika ada
$kelas_info = null;
$tahun_info = null;

if ($id_kelas) {
    $stmt = $conn->prepare("SELECT * FROM kelas WHERE id_kelas = ?");
    $stmt->bind_param("i", $id_kelas);
    $stmt->execute();
    $kelas_info = $stmt->get_result()->fetch_assoc();
}

if ($id_tahun) {
    $stmt = $conn->prepare("SELECT * FROM tahun_pelajaran WHERE id_tahun = ?");
    $stmt->bind_param("i", $id_tahun);
    $stmt->execute();
    $tahun_info = $stmt->get_result()->fetch_assoc();
}

// Get semua kelas untuk dropdown
$stmt = $conn->prepare("SELECT * FROM kelas ORDER BY nama_kelas");
$stmt->execute();
$kelas_list = $stmt->get_result();

// Get semua tahun untuk dropdown
$stmt = $conn->prepare("SELECT * FROM tahun_pelajaran ORDER BY tahun_ajaran DESC");
$stmt->execute();
$tahun_list = $stmt->get_result();

$success = '';
$error = '';

// Handle form submission
if ($_POST) {
    $nama_lengkap = trim($_POST['nama_lengkap']);
    $nama_panggilan = trim($_POST['nama_panggilan']);
    $asal_paroki = trim($_POST['asal_paroki']);
    $keuskupan = trim($_POST['keuskupan']);
    $status_ppdb = $_POST['status_ppdb'];
    $gambaran_umum = trim($_POST['gambaran_umum']);
    $kondisi_saat_ini = trim($_POST['kondisi_saat_ini']);
    $catatan = trim($_POST['catatan']);
    $pendampingan = trim($_POST['pendampingan']);
    $hasil_pendampingan = trim($_POST['hasil_pendampingan']);
    $selected_kelas = $_POST['id_kelas'];
    $selected_tahun = $_POST['id_tahun'];
    $is_potensial = isset($_POST['is_potensial']) ? 1 : 0;
    $is_catatan_khusus = isset($_POST['is_catatan_khusus']) ? 1 : 0;

    // Handle upload foto
    $foto_name = 'placeholder.jpg'; // Default foto

    if (isset($_FILES['foto']) && $_FILES['foto']['error'] == 0) {
        $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        $max_size = 2 * 1024 * 1024; // 2MB

        $file_type = $_FILES['foto']['type'];
        $file_size = $_FILES['foto']['size'];
        $file_tmp = $_FILES['foto']['tmp_name'];

        if (!in_array($file_type, $allowed_types)) {
            $error = "Format file tidak didukung. Gunakan JPG, PNG, atau GIF.";
        } elseif ($file_size > $max_size) {
            $error = "Ukuran file terlalu besar. Maksimal 2MB.";
        } else {
            // Generate nama file unik
            $file_extension = pathinfo($_FILES['foto']['name'], PATHINFO_EXTENSION);
            $foto_name = 'siswa_new_' . time() . '.' . $file_extension;
            $upload_path = '../uploads/' . $foto_name;

            // Upload file
            if (move_uploaded_file($file_tmp, $upload_path)) {
                // Resize foto jika terlalu besar
                resizeImage($upload_path, $upload_path, 400, 400);
            } else {
                $error = "Gagal mengupload foto.";
                $foto_name = 'placeholder.jpg'; // Kembalikan ke default
            }
        }
    }

    if (empty($nama_lengkap)) {
        $error = "Nama lengkap harus diisi.";
    } elseif (empty($selected_kelas) || empty($selected_tahun)) {
        $error = "Kelas dan tahun pelajaran harus dipilih.";
    } elseif (!$error) {
        // Insert data siswa baru
        $stmt = $conn->prepare("
            INSERT INTO siswa (
                nama_lengkap, nama_panggilan, asal_paroki, keuskupan, status_ppdb,
                gambaran_umum, kondisi_saat_ini, catatan, pendampingan, hasil_pendampingan,
                id_kelas, id_tahun, is_potensial, is_catatan_khusus, foto
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");

        $stmt->bind_param("ssssssssssiiiis",
            $nama_lengkap, $nama_panggilan, $asal_paroki, $keuskupan, $status_ppdb,
            $gambaran_umum, $kondisi_saat_ini, $catatan, $pendampingan, $hasil_pendampingan,
            $selected_kelas, $selected_tahun, $is_potensial, $is_catatan_khusus, $foto_name
        );

        if ($stmt->execute()) {
            $success = "Siswa baru berhasil ditambahkan.";
            
            // Reset form
            $_POST = array();
            
            // Update parameter untuk redirect
            $id_kelas = $selected_kelas;
            $id_tahun = $selected_tahun;
        } else {
            $error = "Gagal menambahkan siswa: " . $conn->error;
        }
    }
}
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h3><i class="fas fa-user-plus"></i> Tambah Siswa Baru</h3>
        <?php if ($kelas_info && $tahun_info): ?>
        <p class="text-muted mb-0">
            Target: <?= htmlspecialchars($kelas_info['nama_kelas']) ?> - <?= htmlspecialchars($tahun_info['tahun_ajaran']) ?>
        </p>
        <?php endif; ?>
    </div>
    <div>
        <?php if ($id_kelas && $id_tahun): ?>
        <a href="manage_siswa_kelas.php?kelas=<?= $id_kelas ?>&tahun=<?= $id_tahun ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali ke Kelas
        </a>
        <?php else: ?>
        <a href="view_all_kelas.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
        </a>
        <?php endif; ?>
    </div>
</div>

<?php if ($success): ?>
<div class="alert alert-success alert-dismissible fade show">
    <i class="fas fa-check-circle"></i> <?= $success ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    <?php if ($id_kelas && $id_tahun): ?>
    <hr>
    <a href="manage_siswa_kelas.php?kelas=<?= $id_kelas ?>&tahun=<?= $id_tahun ?>" class="btn btn-success btn-sm">
        <i class="fas fa-list"></i> Lihat Daftar Siswa
    </a>
    <button type="button" class="btn btn-primary btn-sm" onclick="location.reload()">
        <i class="fas fa-plus"></i> Tambah Siswa Lagi
    </button>
    <?php endif; ?>
</div>
<?php endif; ?>

<?php if ($error): ?>
<div class="alert alert-danger alert-dismissible fade show">
    <i class="fas fa-exclamation-circle"></i> <?= $error ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<form method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
    <div class="row">
        <!-- Kolom Kiri -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-user"></i> Data Pribadi</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">Nama Lengkap <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="nama_lengkap" 
                               value="<?= htmlspecialchars($_POST['nama_lengkap'] ?? '') ?>" required>
                        <div class="invalid-feedback">Nama lengkap harus diisi.</div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Nama Panggilan</label>
                        <input type="text" class="form-control" name="nama_panggilan" 
                               value="<?= htmlspecialchars($_POST['nama_panggilan'] ?? '') ?>">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Asal Paroki</label>
                        <input type="text" class="form-control" name="asal_paroki" 
                               value="<?= htmlspecialchars($_POST['asal_paroki'] ?? '') ?>">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Keuskupan</label>
                        <input type="text" class="form-control" name="keuskupan" 
                               value="<?= htmlspecialchars($_POST['keuskupan'] ?? '') ?>">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Status PPDB</label>
                        <select class="form-select" name="status_ppdb">
                            <option value="Pantas" <?= ($_POST['status_ppdb'] ?? '') == 'Pantas' ? 'selected' : '' ?>>Pantas</option>
                            <option value="Diterima" <?= ($_POST['status_ppdb'] ?? '') == 'Diterima' ? 'selected' : '' ?>>Diterima</option>
                            <option value="Dicoba" <?= ($_POST['status_ppdb'] ?? '') == 'Dicoba' ? 'selected' : '' ?>>Dicoba</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h5><i class="fas fa-cog"></i> Pengaturan</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">Kelas <span class="text-danger">*</span></label>
                        <select class="form-select" name="id_kelas" required>
                            <option value="">-- Pilih Kelas --</option>
                            <?php 
                            $kelas_list->data_seek(0);
                            while ($kelas = $kelas_list->fetch_assoc()): 
                            ?>
                            <option value="<?= $kelas['id_kelas'] ?>" 
                                    <?= ($kelas['id_kelas'] == ($id_kelas ?: ($_POST['id_kelas'] ?? ''))) ? 'selected' : '' ?>>
                                <?= htmlspecialchars($kelas['nama_kelas']) ?> - <?= htmlspecialchars($kelas['tingkat']) ?>
                            </option>
                            <?php endwhile; ?>
                        </select>
                        <div class="invalid-feedback">Kelas harus dipilih.</div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Tahun Pelajaran <span class="text-danger">*</span></label>
                        <select class="form-select" name="id_tahun" required>
                            <option value="">-- Pilih Tahun --</option>
                            <?php 
                            $tahun_list->data_seek(0);
                            while ($tahun = $tahun_list->fetch_assoc()): 
                            ?>
                            <option value="<?= $tahun['id_tahun'] ?>" 
                                    <?= ($tahun['id_tahun'] == ($id_tahun ?: ($_POST['id_tahun'] ?? ''))) ? 'selected' : '' ?>>
                                <?= htmlspecialchars($tahun['tahun_ajaran']) ?>
                                <?= $tahun['is_active'] ? ' (Aktif)' : '' ?>
                            </option>
                            <?php endwhile; ?>
                        </select>
                        <div class="invalid-feedback">Tahun pelajaran harus dipilih.</div>
                    </div>

                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" name="is_potensial" id="is_potensial"
                               <?= isset($_POST['is_potensial']) ? 'checked' : '' ?>>
                        <label class="form-check-label" for="is_potensial">
                            <i class="fas fa-star text-warning"></i> Siswa Potensial
                        </label>
                    </div>

                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="is_catatan_khusus" id="is_catatan_khusus"
                               <?= isset($_POST['is_catatan_khusus']) ? 'checked' : '' ?>>
                        <label class="form-check-label" for="is_catatan_khusus">
                            <i class="fas fa-exclamation-triangle text-warning"></i> Catatan Khusus
                        </label>
                    </div>
                </div>
            </div>

            <!-- Upload Foto -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5><i class="fas fa-camera"></i> Foto Siswa</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <img src="../uploads/placeholder.jpg"
                             alt="Preview Foto"
                             class="img-thumbnail" id="preview-foto"
                             style="width: 150px; height: 150px; object-fit: cover;">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Upload Foto</label>
                        <input type="file" class="form-control" name="foto" id="foto"
                               accept="image/jpeg,image/jpg,image/png,image/gif"
                               onchange="previewImage(this)">
                        <div class="form-text">
                            <i class="fas fa-info-circle"></i>
                            Format: JPG, PNG, GIF. Maksimal 2MB. Foto akan otomatis diresize.
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Kolom Kanan -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-clipboard"></i> Data Pendampingan (Opsional)</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">Gambaran Umum</label>
                        <textarea class="form-control" name="gambaran_umum" rows="3"><?= htmlspecialchars($_POST['gambaran_umum'] ?? '') ?></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Kondisi Saat Ini</label>
                        <textarea class="form-control" name="kondisi_saat_ini" rows="3"><?= htmlspecialchars($_POST['kondisi_saat_ini'] ?? '') ?></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Catatan</label>
                        <textarea class="form-control" name="catatan" rows="3"><?= htmlspecialchars($_POST['catatan'] ?? '') ?></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Pendampingan</label>
                        <textarea class="form-control" name="pendampingan" rows="3"><?= htmlspecialchars($_POST['pendampingan'] ?? '') ?></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Hasil Pendampingan</label>
                        <textarea class="form-control" name="hasil_pendampingan" rows="3"><?= htmlspecialchars($_POST['hasil_pendampingan'] ?? '') ?></textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-3">
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-save"></i> Tambah Siswa
                    </button>
                    <?php if ($id_kelas && $id_tahun): ?>
                    <a href="manage_siswa_kelas.php?kelas=<?= $id_kelas ?>&tahun=<?= $id_tahun ?>" class="btn btn-secondary btn-lg ms-2">
                        <i class="fas fa-times"></i> Batal
                    </a>
                    <?php else: ?>
                    <a href="view_all_kelas.php" class="btn btn-secondary btn-lg ms-2">
                        <i class="fas fa-times"></i> Batal
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</form>

<script>
// Preview foto sebelum upload
function previewImage(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('preview-foto').src = e.target.result;
        };
        reader.readAsDataURL(input.files[0]);
    }
}

// Bootstrap form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

<?php include '../includes/footer.php'; ?>
