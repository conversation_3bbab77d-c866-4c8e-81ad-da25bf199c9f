<?php
// admin/dashboard.php

// Include header yang sudah menghandle autentikasi
include '../includes/header.php';

// Cek apakah user adalah admin
if ($_SESSION['role'] !== 'admin') {
    die('<div class="alert alert-danger">Akses ditolak. Halaman ini hanya untuk administrator.</div>');
}

// Statistik dashboard
$stats = [];

// Total users
$stmt = $conn->prepare("SELECT COUNT(*) as total FROM users WHERE is_active = 1");
$stmt->execute();
$stats['total_users'] = $stmt->get_result()->fetch_assoc()['total'];

// Total wali kelas
$stmt = $conn->prepare("SELECT COUNT(*) as total FROM users WHERE role = 'wali_kelas' AND is_active = 1");
$stmt->execute();
$stats['total_wali'] = $stmt->get_result()->fetch_assoc()['total'];

// Total kelas
$stmt = $conn->prepare("SELECT COUNT(*) as total FROM kelas");
$stmt->execute();
$stats['total_kelas'] = $stmt->get_result()->fetch_assoc()['total'];

// Total siswa tahun aktif
$tahun_aktif = getActiveTahun($conn);
$stmt = $conn->prepare("SELECT COUNT(*) as total FROM siswa WHERE id_tahun = ?");
$stmt->bind_param("i", $tahun_aktif['id_tahun']);
$stmt->execute();
$stats['total_siswa'] = $stmt->get_result()->fetch_assoc()['total'];

// Kelas tanpa wali
$stmt = $conn->prepare("
    SELECT k.id_kelas, k.nama_kelas 
    FROM kelas k 
    LEFT JOIN user_kelas uk ON k.id_kelas = uk.id_kelas AND uk.id_tahun = ?
    WHERE uk.id_kelas IS NULL
    ORDER BY k.nama_kelas
");
$stmt->bind_param("i", $tahun_aktif['id_tahun']);
$stmt->execute();
$kelas_tanpa_wali = $stmt->get_result();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h3><i class="fas fa-tachometer-alt"></i> Dashboard Administrator</h3>
    <div>
        <span class="badge bg-primary">Tahun Aktif: <?= htmlspecialchars($tahun_aktif['tahun_ajaran']) ?></span>
    </div>
</div>

<!-- Statistik Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4><?= $stats['total_users'] ?></h4>
                        <p class="mb-0">Total Users</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4><?= $stats['total_wali'] ?></h4>
                        <p class="mb-0">Wali Kelas</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chalkboard-teacher fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4><?= $stats['total_kelas'] ?></h4>
                        <p class="mb-0">Total Kelas</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-school fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4><?= $stats['total_siswa'] ?></h4>
                        <p class="mb-0">Total Siswa</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-graduate fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-bolt"></i> Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="manage_users.php" class="btn btn-primary btn-block w-100">
                            <i class="fas fa-users"></i> Kelola Users
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="manage_kelas.php" class="btn btn-success btn-block w-100">
                            <i class="fas fa-school"></i> Kelola Kelas
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="manage_tahun.php" class="btn btn-info btn-block w-100">
                            <i class="fas fa-calendar"></i> Tahun Pelajaran
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="view_all_kelas.php" class="btn btn-warning btn-block w-100">
                            <i class="fas fa-chart-bar"></i> Akses Semua Kelas
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Kelas Tanpa Wali -->
<?php if ($kelas_tanpa_wali->num_rows > 0): ?>
<div class="row">
    <div class="col-md-12">
        <div class="card border-warning">
            <div class="card-header bg-warning text-dark">
                <h5><i class="fas fa-exclamation-triangle"></i> Kelas Tanpa Wali (Tahun <?= htmlspecialchars($tahun_aktif['tahun_ajaran']) ?>)</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> 
                    Kelas-kelas berikut belum memiliki wali kelas untuk tahun pelajaran aktif.
                </div>
                <div class="row">
                    <?php while($kelas = $kelas_tanpa_wali->fetch_assoc()): ?>
                        <div class="col-md-4 mb-2">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h6><?= htmlspecialchars($kelas['nama_kelas']) ?></h6>
                                    <a href="assign_wali.php?kelas=<?= $kelas['id_kelas'] ?>&tahun=<?= $tahun_aktif['id_tahun'] ?>" 
                                       class="btn btn-sm btn-primary">
                                        <i class="fas fa-plus"></i> Assign Wali
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endwhile; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<?php include '../includes/footer.php'; ?>
