<?php
// admin/delete_siswa.php - Hapus siswa oleh admin

include '../includes/header.php';

// Cek apakah user adalah admin
if ($_SESSION['role'] !== 'admin') {
    die('<div class="alert alert-danger">Aks<PERSON> ditolak. Halaman ini hanya untuk administrator.</div>');
}

$id_siswa = $_GET['id'] ?? 0;
$id_kelas = $_GET['kelas'] ?? 0;
$id_tahun = $_GET['tahun'] ?? 0;

if (!$id_siswa) {
    die('<div class="alert alert-danger">ID siswa tidak valid.</div>');
}

// Get data siswa untuk konfirmasi
$stmt = $conn->prepare("
    SELECT s.*, k.nama_kelas, tp.tahun_ajaran 
    FROM siswa s 
    JOIN kelas k ON s.id_kelas = k.id_kelas 
    JOIN tahun_pelajaran tp ON s.id_tahun = tp.id_tahun 
    WHERE s.id_siswa = ?
");
$stmt->bind_param("i", $id_siswa);
$stmt->execute();
$siswa = $stmt->get_result()->fetch_assoc();

if (!$siswa) {
    die('<div class="alert alert-danger">Data siswa tidak ditemukan.</div>');
}

$success = '';
$error = '';

// Handle konfirmasi hapus
if ($_POST && isset($_POST['confirm_delete'])) {
    // Hapus foto jika bukan placeholder
    if ($siswa['foto'] && $siswa['foto'] !== 'placeholder.jpg') {
        $foto_path = '../uploads/' . $siswa['foto'];
        if (file_exists($foto_path)) {
            unlink($foto_path);
        }
    }
    
    // Hapus data siswa
    $stmt = $conn->prepare("DELETE FROM siswa WHERE id_siswa = ?");
    $stmt->bind_param("i", $id_siswa);
    
    if ($stmt->execute()) {
        $success = "Data siswa berhasil dihapus.";
        
        // Redirect setelah 2 detik
        echo '<script>
            setTimeout(function() {
                window.location.href = "manage_siswa_kelas.php?kelas=' . $id_kelas . '&tahun=' . $id_tahun . '";
            }, 2000);
        </script>';
    } else {
        $error = "Gagal menghapus data siswa: " . $conn->error;
    }
}
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h3><i class="fas fa-user-times text-danger"></i> Hapus Siswa</h3>
        <p class="text-muted mb-0">
            Kelas: <?= htmlspecialchars($siswa['nama_kelas']) ?> | 
            Tahun: <?= htmlspecialchars($siswa['tahun_ajaran']) ?>
        </p>
    </div>
    <div>
        <a href="manage_siswa_kelas.php?kelas=<?= $id_kelas ?>&tahun=<?= $id_tahun ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
        </a>
    </div>
</div>

<?php if ($success): ?>
<div class="alert alert-success">
    <i class="fas fa-check-circle"></i> <?= $success ?>
    <div class="mt-2">
        <i class="fas fa-spinner fa-spin"></i> Mengalihkan ke daftar siswa...
    </div>
</div>
<?php endif; ?>

<?php if ($error): ?>
<div class="alert alert-danger">
    <i class="fas fa-exclamation-circle"></i> <?= $error ?>
</div>
<?php endif; ?>

<?php if (!$success): ?>
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle"></i> Konfirmasi Penghapusan
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Peringatan!</strong> Tindakan ini tidak dapat dibatalkan. 
                    Semua data siswa akan dihapus secara permanen.
                </div>

                <!-- Data Siswa yang akan dihapus -->
                <div class="row">
                    <div class="col-md-3 text-center">
                        <img src="../uploads/<?= htmlspecialchars($siswa['foto'] ?: 'placeholder.jpg') ?>" 
                             alt="Foto <?= htmlspecialchars($siswa['nama_lengkap']) ?>"
                             class="img-thumbnail" style="width: 120px; height: 120px; object-fit: cover;"
                             onerror="this.src='../uploads/placeholder.jpg'">
                    </div>
                    <div class="col-md-9">
                        <table class="table table-borderless">
                            <tr>
                                <td width="150"><strong>Nama Lengkap:</strong></td>
                                <td><?= htmlspecialchars($siswa['nama_lengkap']) ?></td>
                            </tr>
                            <tr>
                                <td><strong>Nama Panggilan:</strong></td>
                                <td><?= htmlspecialchars($siswa['nama_panggilan']) ?></td>
                            </tr>
                            <tr>
                                <td><strong>Kelas:</strong></td>
                                <td><?= htmlspecialchars($siswa['nama_kelas']) ?></td>
                            </tr>
                            <tr>
                                <td><strong>Tahun Pelajaran:</strong></td>
                                <td><?= htmlspecialchars($siswa['tahun_ajaran']) ?></td>
                            </tr>
                            <tr>
                                <td><strong>Asal Paroki:</strong></td>
                                <td><?= htmlspecialchars($siswa['asal_paroki']) ?></td>
                            </tr>
                            <tr>
                                <td><strong>Status PPDB:</strong></td>
                                <td>
                                    <span class="badge bg-<?= $siswa['status_ppdb'] == 'Beasiswa' ? 'success' : 'primary' ?>">
                                        <?= htmlspecialchars($siswa['status_ppdb']) ?>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Status Khusus:</strong></td>
                                <td>
                                    <?php if ($siswa['is_potensial']): ?>
                                        <span class="badge bg-success me-1">Potensial</span>
                                    <?php endif; ?>
                                    <?php if ($siswa['is_catatan_khusus']): ?>
                                        <span class="badge bg-warning">Catatan Khusus</span>
                                    <?php endif; ?>
                                    <?php if (!$siswa['is_potensial'] && !$siswa['is_catatan_khusus']): ?>
                                        <em class="text-muted">Tidak ada</em>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- Data Pendampingan (jika ada) -->
                <?php if ($siswa['gambaran_umum'] || $siswa['kondisi_saat_ini'] || $siswa['catatan'] || $siswa['pendampingan'] || $siswa['hasil_pendampingan']): ?>
                <hr>
                <h6 class="text-danger">Data Pendampingan yang akan ikut terhapus:</h6>
                <div class="row">
                    <?php if ($siswa['gambaran_umum']): ?>
                    <div class="col-md-6 mb-2">
                        <small class="text-muted">Gambaran Umum:</small>
                        <div class="border p-2 small"><?= nl2br(htmlspecialchars(substr($siswa['gambaran_umum'], 0, 100))) ?>...</div>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($siswa['kondisi_saat_ini']): ?>
                    <div class="col-md-6 mb-2">
                        <small class="text-muted">Kondisi Saat Ini:</small>
                        <div class="border p-2 small"><?= nl2br(htmlspecialchars(substr($siswa['kondisi_saat_ini'], 0, 100))) ?>...</div>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($siswa['catatan']): ?>
                    <div class="col-md-6 mb-2">
                        <small class="text-muted">Catatan:</small>
                        <div class="border p-2 small"><?= nl2br(htmlspecialchars(substr($siswa['catatan'], 0, 100))) ?>...</div>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($siswa['pendampingan']): ?>
                    <div class="col-md-6 mb-2">
                        <small class="text-muted">Pendampingan:</small>
                        <div class="border p-2 small"><?= nl2br(htmlspecialchars(substr($siswa['pendampingan'], 0, 100))) ?>...</div>
                    </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>

                <hr>
                <div class="text-center">
                    <form method="POST" style="display: inline;">
                        <button type="submit" name="confirm_delete" class="btn btn-danger btn-lg me-3" 
                                onclick="return confirm('Apakah Anda BENAR-BENAR yakin ingin menghapus siswa ini?\n\nTindakan ini TIDAK DAPAT DIBATALKAN!')">
                            <i class="fas fa-trash"></i> Ya, Hapus Siswa
                        </button>
                    </form>
                    
                    <a href="manage_siswa_kelas.php?kelas=<?= $id_kelas ?>&tahun=<?= $id_tahun ?>" class="btn btn-secondary btn-lg">
                        <i class="fas fa-times"></i> Batal
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<?php include '../includes/footer.php'; ?>
