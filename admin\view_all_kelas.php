<?php
// admin/view_all_kelas.php - Admin dapat mengakses semua kelas

include '../includes/header.php';

// Cek apakah user adalah admin
if ($_SESSION['role'] !== 'admin') {
    die('<div class="alert alert-danger">Aks<PERSON> ditolak. Halaman ini hanya untuk administrator.</div>');
}

// Get tahun aktif
$tahun_aktif = getActiveTahun($conn);

// Get semua kelas dengan detail
$stmt = $conn->prepare("
    SELECT k.*, 
           COUNT(s.id_siswa) as jumlah_siswa,
           COUNT(CASE WHEN s.is_potensial = 1 THEN 1 END) as siswa_potensial,
           COUNT(CASE WHEN s.is_catatan_khusus = 1 THEN 1 END) as siswa_catatan_khusus,
           u.nama_lengkap as wali_kelas,
           u.id_user as id_wali
    FROM kelas k
    LEFT JOIN siswa s ON k.id_kelas = s.id_kelas AND s.id_tahun = ?
    LEFT JOIN user_kelas uk ON k.id_kelas = uk.id_kelas AND uk.id_tahun = ?
    LEFT JOIN users u ON uk.id_user = u.id_user
    GROUP BY k.id_kelas
    ORDER BY k.nama_kelas
");
$stmt->bind_param("ii", $tahun_aktif['id_tahun'], $tahun_aktif['id_tahun']);
$stmt->execute();
$kelas_list = $stmt->get_result();
?>

<!-- Modern Light Header -->
<div class="container-fluid px-0">
    <div class="bg-gradient-primary-to-secondary py-5 mb-5 rounded-3" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-6 text-white fw-bold mb-2">
                        <i class="fas fa-graduation-cap me-3"></i>Manajemen Kelas
                    </h1>
                    <p class="text-white-50 mb-0 fs-5">
                        Kelola dan pantau semua kelas dalam sistem Cura Personalis
                    </p>
                </div>
                <div class="col-lg-4 text-lg-end">
                    <div class="d-flex flex-column align-items-lg-end">
                        <span class="badge bg-white text-primary fs-6 mb-2 px-3 py-2">
                            <i class="fas fa-calendar-alt me-2"></i>
                            <?= htmlspecialchars($tahun_aktif['tahun_ajaran']) ?>
                        </span>
                        <a href="dashboard.php" class="btn btn-light btn-lg">
                            <i class="fas fa-arrow-left me-2"></i>Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modern Light Cards Grid -->
<div class="container">
    <div class="row g-4">
        <?php while ($kelas = $kelas_list->fetch_assoc()): ?>
        <div class="col-md-6 col-xl-4">
            <div class="card border-0 shadow-lg h-100 hover-lift" style="transition: all 0.3s ease;">
                <div class="card-header border-0 bg-white pt-4 pb-0">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h4 class="fw-bold text-dark mb-2">
                                <i class="fas fa-graduation-cap text-primary me-2"></i>
                                <?= htmlspecialchars($kelas['nama_kelas']) ?>
                            </h4>
                            <span class="badge bg-primary bg-opacity-10 text-primary rounded-pill px-3 py-2">
                                <i class="fas fa-layer-group me-1"></i>
                                <?= htmlspecialchars($kelas['tingkat']) ?>
                            </span>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-light btn-sm rounded-circle shadow-sm" data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-v text-muted"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end shadow">
                                <li><h6 class="dropdown-header">Aksi Kelas</h6></li>
                                <li><a class="dropdown-item" href="../guru/gambaran_kelas.php?kelas=<?= $kelas['id_kelas'] ?>&tahun=<?= $tahun_aktif['id_tahun'] ?>">
                                    <i class="fas fa-chart-line text-primary me-2"></i>Gambaran Kelas
                                </a></li>
                                <li><a class="dropdown-item" href="../guru/data_siswa.php?kelas=<?= $kelas['id_kelas'] ?>&tahun=<?= $tahun_aktif['id_tahun'] ?>">
                                    <i class="fas fa-users text-success me-2"></i>Data Siswa
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="add_siswa.php?kelas=<?= $kelas['id_kelas'] ?>&tahun=<?= $tahun_aktif['id_tahun'] ?>">
                                    <i class="fas fa-user-plus text-info me-2"></i>Tambah Siswa
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="card-body px-4 pb-4">
                    <!-- Wali Kelas Info -->
                    <div class="d-flex align-items-center mb-4 p-3 bg-light bg-opacity-50 rounded-3">
                        <div class="flex-shrink-0">
                            <div class="avatar-sm bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center">
                                <i class="fas fa-user-tie text-primary"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="small text-muted mb-1">Wali Kelas</div>
                            <?php if ($kelas['wali_kelas']): ?>
                                <div class="fw-semibold text-dark"><?= htmlspecialchars($kelas['wali_kelas']) ?></div>
                            <?php else: ?>
                                <div class="text-warning fw-semibold">
                                    <i class="fas fa-exclamation-triangle me-1"></i>Belum ditentukan
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Modern Stats Grid -->
                    <div class="row g-3 mb-4">
                        <div class="col-4">
                            <div class="text-center p-3 bg-primary bg-opacity-5 rounded-3 border border-primary border-opacity-10">
                                <div class="h4 fw-bold text-primary mb-1"><?= $kelas['jumlah_siswa'] ?></div>
                                <div class="small text-muted">Total Siswa</div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="text-center p-3 bg-success bg-opacity-5 rounded-3 border border-success border-opacity-10">
                                <div class="h4 fw-bold text-success mb-1"><?= $kelas['siswa_potensial'] ?></div>
                                <div class="small text-muted">Potensial</div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="text-center p-3 bg-warning bg-opacity-5 rounded-3 border border-warning border-opacity-10">
                                <div class="h4 fw-bold text-warning mb-1"><?= $kelas['siswa_catatan_khusus'] ?></div>
                                <div class="small text-muted">Perhatian</div>
                            </div>
                        </div>
                    </div>

                    <!-- Modern Action Buttons -->
                    <div class="d-grid gap-2">
                        <a href="../guru/gambaran_kelas.php?kelas=<?= $kelas['id_kelas'] ?>&tahun=<?= $tahun_aktif['id_tahun'] ?>"
                           class="btn btn-primary rounded-pill shadow-sm">
                            <i class="fas fa-chart-line me-2"></i>Gambaran Kelas
                        </a>

                        <div class="row g-2">
                            <div class="col-6">
                                <a href="../guru/data_siswa.php?kelas=<?= $kelas['id_kelas'] ?>&tahun=<?= $tahun_aktif['id_tahun'] ?>"
                                   class="btn btn-outline-primary btn-sm rounded-pill w-100">
                                    <i class="fas fa-users me-1"></i>Data Siswa
                                </a>
                            </div>
                            <div class="col-6">
                                <a href="add_siswa.php?kelas=<?= $kelas['id_kelas'] ?>&tahun=<?= $tahun_aktif['id_tahun'] ?>"
                                   class="btn btn-outline-success btn-sm rounded-pill w-100">
                                    <i class="fas fa-user-plus me-1"></i>Tambah
                                </a>
                            </div>
                        </div>

                        <?php if (!$kelas['wali_kelas']): ?>
                        <a href="assign_wali.php?kelas=<?= $kelas['id_kelas'] ?>&tahun=<?= $tahun_aktif['id_tahun'] ?>"
                           class="btn btn-outline-warning btn-sm rounded-pill">
                            <i class="fas fa-user-tie me-2"></i>Assign Wali Kelas
                        </a>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Modern Footer -->
                <div class="card-footer bg-transparent border-0 pt-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            <i class="fas fa-calendar-alt me-1"></i>
                            <?= date('d M Y', strtotime($kelas['created_at'])) ?>
                        </small>
                        <div class="d-flex gap-1">
                            <?php if ($kelas['jumlah_siswa'] > 0): ?>
                                <span class="badge bg-success bg-opacity-10 text-success">Aktif</span>
                            <?php else: ?>
                                <span class="badge bg-secondary bg-opacity-10 text-secondary">Kosong</span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
        </div>
    </div>
    <?php endwhile; ?>
</div>

<?php if ($kelas_list->num_rows == 0): ?>
<div class="text-center py-5">
    <i class="fas fa-school fa-3x text-muted mb-3"></i>
    <h4 class="text-muted">Belum Ada Kelas</h4>
    <p class="text-muted">Silakan tambah kelas terlebih dahulu.</p>
    <a href="manage_kelas.php" class="btn btn-primary">
        <i class="fas fa-plus"></i> Tambah Kelas
    </a>
</div>
<?php endif; ?>

<!-- Modal untuk Quick Actions -->
<div class="modal fade" id="quickActionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Quick Actions</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="d-grid gap-2">
                    <a href="manage_kelas.php" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Tambah Kelas Baru
                    </a>
                    <a href="manage_users.php" class="btn btn-success">
                        <i class="fas fa-user-plus"></i> Tambah Wali Kelas
                    </a>
                    <a href="bulk_import.php" class="btn btn-info">
                        <i class="fas fa-upload"></i> Import Data Siswa
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Floating Action Button -->
<div class="position-fixed bottom-0 end-0 p-3">
    <button class="btn btn-primary btn-lg rounded-circle" data-bs-toggle="modal" data-bs-target="#quickActionModal">
        <i class="fas fa-plus"></i>
    </button>
</div>

<style>
.border-end {
    border-right: 1px solid #dee2e6 !important;
}
</style>

<?php include '../includes/footer.php'; ?>
