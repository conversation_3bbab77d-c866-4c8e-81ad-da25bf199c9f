/* Modern Light Theme for Cura Personalis */

/* ===== GLOBAL STYLES ===== */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #3b82f6;
    --light-color: #f8fafc;
    --dark-color: #1e293b;
    --border-radius: 12px;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #f8fafc;
    color: #334155;
    line-height: 1.6;
}

/* ===== MODERN CARDS ===== */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    background: white;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.hover-lift {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

/* ===== MODERN BUTTONS ===== */
.btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: all 0.2s ease;
    border: none;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

.btn-outline-primary {
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-warning {
    background: var(--warning-color);
    color: white;
}

.btn-light {
    background: white;
    color: var(--dark-color);
    border: 1px solid #e2e8f0;
}

.btn-light:hover {
    background: #f1f5f9;
    border-color: #cbd5e1;
}

/* ===== MODERN BADGES ===== */
.badge {
    font-weight: 500;
    padding: 0.375rem 0.75rem;
    border-radius: 6px;
}

.bg-primary.bg-opacity-10 {
    background-color: rgba(102, 126, 234, 0.1) !important;
}

.bg-success.bg-opacity-10 {
    background-color: rgba(16, 185, 129, 0.1) !important;
}

.bg-warning.bg-opacity-10 {
    background-color: rgba(245, 158, 11, 0.1) !important;
}

.bg-secondary.bg-opacity-10 {
    background-color: rgba(107, 114, 128, 0.1) !important;
}

/* ===== MODERN STATS CARDS ===== */
.stats-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid #f1f5f9;
    transition: all 0.2s ease;
}

.stats-card:hover {
    box-shadow: var(--shadow);
    transform: translateY(-1px);
}

/* ===== MODERN GRADIENTS ===== */
.bg-gradient-primary-to-secondary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.bg-gradient-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.bg-gradient-warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

/* ===== MODERN TABLES ===== */
.table {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.table thead th {
    background: #f8fafc;
    border: none;
    font-weight: 600;
    color: var(--dark-color);
    padding: 1rem;
}

.table tbody td {
    border: none;
    padding: 1rem;
    border-bottom: 1px solid #f1f5f9;
}

.table tbody tr:hover {
    background: #f8fafc;
}

/* ===== MODERN FORMS ===== */
.form-control {
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 0.75rem;
    transition: all 0.2s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-label {
    font-weight: 500;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

/* ===== MODERN AVATARS ===== */
.avatar-sm {
    width: 2.5rem;
    height: 2.5rem;
}

.avatar-md {
    width: 3rem;
    height: 3rem;
}

.avatar-lg {
    width: 4rem;
    height: 4rem;
}

/* ===== MODERN DROPDOWNS ===== */
.dropdown-menu {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    padding: 0.5rem;
}

.dropdown-item {
    border-radius: 6px;
    padding: 0.5rem 0.75rem;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background: #f1f5f9;
    color: var(--dark-color);
}

.dropdown-header {
    font-weight: 600;
    color: var(--dark-color);
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* ===== MODERN ALERTS ===== */
.alert {
    border: none;
    border-radius: var(--border-radius);
    padding: 1rem 1.25rem;
}

.alert-success {
    background: rgba(16, 185, 129, 0.1);
    color: #065f46;
    border-left: 4px solid var(--success-color);
}

.alert-warning {
    background: rgba(245, 158, 11, 0.1);
    color: #92400e;
    border-left: 4px solid var(--warning-color);
}

.alert-danger {
    background: rgba(239, 68, 68, 0.1);
    color: #991b1b;
    border-left: 4px solid var(--danger-color);
}

.alert-info {
    background: rgba(59, 130, 246, 0.1);
    color: #1e40af;
    border-left: 4px solid var(--info-color);
}

/* ===== MODERN NAVIGATION ===== */
.navbar {
    background: white !important;
    box-shadow: var(--shadow-sm);
    border-bottom: 1px solid #f1f5f9;
}

.navbar-brand {
    font-weight: 700;
    color: var(--primary-color) !important;
}

.nav-link {
    color: var(--dark-color) !important;
    font-weight: 500;
    transition: all 0.2s ease;
}

.nav-link:hover {
    color: var(--primary-color) !important;
}

/* ===== MODERN SPACING ===== */
.section-padding {
    padding: 4rem 0;
}

.card-padding {
    padding: 2rem;
}

/* ===== RESPONSIVE IMPROVEMENTS ===== */
@media (max-width: 768px) {
    .card {
        margin-bottom: 1rem;
    }
    
    .btn {
        font-size: 0.875rem;
        padding: 0.5rem 0.75rem;
    }
    
    .stats-card {
        padding: 1rem;
    }
}

/* ===== MODERN ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* ===== MODERN UTILITIES ===== */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.glass-effect {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.border-gradient {
    border: 2px solid transparent;
    background: linear-gradient(white, white) padding-box,
                linear-gradient(135deg, var(--primary-color), var(--secondary-color)) border-box;
}
