<?php
// guru/data_siswa.php

// Include header yang sudah menghandle autentikasi
include '../includes/header.php';

$id_kelas = $_GET['kelas'] ?? $_SESSION['current_kelas'] ?? null;
$id_tahun = $_GET['tahun'] ?? $_SESSION['current_tahun'] ?? null;

// Validasi parameter dan akses
if (!$id_kelas || !$id_tahun) {
    die('<div class="alert alert-danger">Parameter kelas dan tahun harus diisi.</div>');
}

// Cek apakah user dapat mengakses kelas ini
requireKelasAccess($conn, $id_kelas, $id_tahun);

// Ambil data kelas
$stmt = $conn->prepare("SELECT nama_kelas FROM kelas WHERE id_kelas = ?");
$stmt->bind_param("i", $id_kelas);
$stmt->execute();
$kelas = $stmt->get_result()->fetch_assoc();

// Ambil data siswa dengan foto
$stmt = $conn->prepare("
    SELECT id_siswa, nama_lengkap, nama_panggilan, asal_paroki, keuskupan, status_ppdb,
           is_potensial, is_catatan_khusus, foto,
           CASE WHEN foto IS NULL OR foto = '' THEN 'placeholder.jpg' ELSE foto END as foto_display
    FROM siswa
    WHERE id_kelas = ? AND id_tahun = ?
    ORDER BY nama_lengkap
");
$stmt->bind_param("ii", $id_kelas, $id_tahun);
$stmt->execute();
$siswa_list = $stmt->get_result();
?>

<!-- Modern Light Header -->
<div class="bg-gradient-primary-to-secondary py-4 mb-5 rounded-3">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="text-white fw-bold mb-2">
                    <i class="fas fa-users me-3"></i><?= htmlspecialchars($kelas['nama_kelas']) ?>
                </h1>
                <?php
                // Get tahun pelajaran
                $stmt = $conn->prepare("SELECT tahun_ajaran FROM tahun_pelajaran WHERE id_tahun = ?");
                $stmt->bind_param("i", $id_tahun);
                $stmt->execute();
                $tahun = $stmt->get_result()->fetch_assoc();
                ?>
                <p class="text-white-50 mb-0 fs-5">
                    <i class="fas fa-calendar-alt me-2"></i>
                    Tahun Pelajaran: <?= htmlspecialchars($tahun['tahun_ajaran']) ?>
                </p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <div class="d-flex flex-column flex-lg-row gap-2 align-items-lg-end">
                    <a href="gambaran_kelas.php?kelas=<?= $id_kelas ?>&tahun=<?= $id_tahun ?>" class="btn btn-light">
                        <i class="fas fa-chart-line me-2"></i>Gambaran Kelas
                    </a>
                    <?php if ($_SESSION['role'] === 'admin'): ?>
                    <a href="../admin/add_siswa.php?kelas=<?= $id_kelas ?>&tahun=<?= $id_tahun ?>" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>Tambah Siswa
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php if ($siswa_list->num_rows > 0): ?>
    <!-- Modern Table Card -->
    <div class="card border-0 shadow-lg">
        <div class="card-header bg-white border-0 py-3">
            <h5 class="mb-0 text-dark fw-semibold">
                <i class="fas fa-table me-2 text-primary"></i>Daftar Siswa
            </h5>
        </div>
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);"
                    <tr class="text-white">
                        <th width="50" class="border-0 fw-semibold">No</th>
                        <th width="60" class="border-0 fw-semibold">Foto</th>
                        <th class="border-0 fw-semibold">Nama Lengkap</th>
                        <th class="border-0 fw-semibold">Nama Panggilan</th>
                        <th class="border-0 fw-semibold">Asal Paroki</th>
                        <th class="border-0 fw-semibold">Keuskupan</th>
                        <th class="border-0 fw-semibold">Status PPDB</th>
                        <th class="border-0 fw-semibold">Status Khusus</th>
                        <th width="200" class="border-0 fw-semibold">Aksi</th>
                    </tr>
            </thead>
            <tbody>
                <?php $no = 1; while($siswa = $siswa_list->fetch_assoc()): ?>
                    <tr>
                        <td><?= $no++ ?></td>
                        <td class="align-middle">
                            <div class="avatar-sm">
                                <img src="../uploads/<?= htmlspecialchars($siswa['foto_display']) ?>"
                                     alt="Foto <?= htmlspecialchars($siswa['nama_lengkap']) ?>"
                                     class="rounded-circle shadow-sm" width="40" height="40" style="object-fit: cover;"
                                     onerror="this.src='../uploads/placeholder.jpg'">
                            </div>
                        </td>
                        <td class="align-middle">
                            <div class="fw-semibold text-dark"><?= htmlspecialchars($siswa['nama_lengkap']) ?></div>
                        </td>
                        <td class="align-middle text-muted"><?= htmlspecialchars($siswa['nama_panggilan'] ?: '-') ?></td>
                        <td class="align-middle"><?= htmlspecialchars($siswa['asal_paroki'] ?: '-') ?></td>
                        <td class="align-middle"><?= htmlspecialchars($siswa['keuskupan'] ?: '-') ?></td>
                        <td class="align-middle">
                            <span class="badge rounded-pill bg-<?= $siswa['status_ppdb'] == 'Pantas' ? 'success' : ($siswa['status_ppdb'] == 'Diterima' ? 'primary' : 'warning') ?> bg-opacity-10 text-<?= $siswa['status_ppdb'] == 'Pantas' ? 'success' : ($siswa['status_ppdb'] == 'Diterima' ? 'primary' : 'warning') ?>">
                                <?= htmlspecialchars($siswa['status_ppdb']) ?>
                            </span>
                        </td>
                        <td class="align-middle">
                            <div class="d-flex gap-1">
                                <?php if ($siswa['is_potensial']): ?>
                                    <span class="badge rounded-pill bg-success bg-opacity-10 text-success">
                                        <i class="fas fa-star me-1"></i>Potensial
                                    </span>
                                <?php endif; ?>
                                <?php if ($siswa['is_catatan_khusus']): ?>
                                    <span class="badge rounded-pill bg-warning bg-opacity-10 text-warning">
                                        <i class="fas fa-exclamation-triangle me-1"></i>Perhatian
                                    </span>
                                <?php endif; ?>
                                <?php if (!$siswa['is_potensial'] && !$siswa['is_catatan_khusus']): ?>
                                    <span class="text-muted">-</span>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td class="align-middle">
                            <div class="d-flex gap-1">
                                <a href="detail_siswa.php?id=<?= $siswa['id_siswa'] ?>&kelas=<?= $id_kelas ?>&tahun=<?= $id_tahun ?>"
                                   class="btn btn-sm btn-outline-primary rounded-pill" title="Lihat Detail">
                                    <i class="fas fa-eye"></i>
                                </a>

                                <?php if ($_SESSION['role'] === 'admin' || $_SESSION['role'] === 'wali_kelas'): ?>
                                <a href="edit_siswa.php?id=<?= $siswa['id_siswa'] ?>&kelas=<?= $id_kelas ?>&tahun=<?= $id_tahun ?>"
                                   class="btn btn-sm btn-outline-warning rounded-pill" title="Edit Siswa">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <?php endif; ?>

                                <?php if ($_SESSION['role'] === 'admin'): ?>
                                <button class="btn btn-sm btn-outline-danger rounded-pill" title="Hapus Siswa"
                                        onclick="confirmDelete(<?= $siswa['id_siswa'] ?>, '<?= htmlspecialchars($siswa['nama_lengkap']) ?>')">
                                    <i class="fas fa-trash"></i>
                                </button>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                <?php endwhile; ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Modern Info Summary -->
    <div class="card border-0 shadow-sm mt-4">
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <?php
                    // Hitung statistik
                    $siswa_list->data_seek(0);
                    $potensial = 0;
                    $catatan_khusus = 0;
                    while ($s = $siswa_list->fetch_assoc()) {
                        if ($s['is_potensial']) $potensial++;
                        if ($s['is_catatan_khusus']) $catatan_khusus++;
                    }
                    ?>
                    <div class="d-flex align-items-center gap-4">
                        <div class="d-flex align-items-center">
                            <div class="avatar-sm bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-3">
                                <i class="fas fa-users text-primary"></i>
                            </div>
                            <div>
                                <div class="fw-bold text-dark"><?= $siswa_list->num_rows ?></div>
                                <small class="text-muted">Total Siswa</small>
                            </div>
                        </div>

                        <div class="d-flex align-items-center">
                            <div class="avatar-sm bg-success bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-3">
                                <i class="fas fa-star text-success"></i>
                            </div>
                            <div>
                                <div class="fw-bold text-success"><?= $potensial ?></div>
                                <small class="text-muted">Potensial</small>
                            </div>
                        </div>

                        <div class="d-flex align-items-center">
                            <div class="avatar-sm bg-warning bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-3">
                                <i class="fas fa-exclamation-triangle text-warning"></i>
                            </div>
                            <div>
                                <div class="fw-bold text-warning"><?= $catatan_khusus ?></div>
                                <small class="text-muted">Perhatian</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-md-end">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        Klik <span class="badge bg-primary bg-opacity-10 text-primary"><i class="fas fa-eye"></i></span> untuk detail,
                        <span class="badge bg-warning bg-opacity-10 text-warning"><i class="fas fa-edit"></i></span> untuk edit
                    </small>
                </div>
            </div>
        </div>
    </div>

<?php else: ?>
    <div class="text-center py-5">
        <i class="fas fa-users fa-3x text-muted mb-3"></i>
        <h5 class="text-muted">Belum Ada Siswa</h5>
        <p class="text-muted">Kelas ini belum memiliki siswa untuk tahun pelajaran ini.</p>

        <?php if ($_SESSION['role'] === 'admin'): ?>
        <a href="../admin/add_siswa.php?kelas=<?= $id_kelas ?>&tahun=<?= $id_tahun ?>" class="btn btn-primary">
            <i class="fas fa-plus"></i> Tambah Siswa Pertama
        </a>
        <?php endif; ?>
    </div>
<?php endif; ?>

<script>
function confirmDelete(id, nama) {
    if (confirm(`Apakah Anda yakin ingin menghapus siswa "${nama}"?\n\nTindakan ini tidak dapat dibatalkan.`)) {
        window.location.href = `../admin/delete_siswa.php?id=${id}&kelas=<?= $id_kelas ?>&tahun=<?= $id_tahun ?>`;
    }
}
</script>

<?php include '../includes/footer.php'; ?>