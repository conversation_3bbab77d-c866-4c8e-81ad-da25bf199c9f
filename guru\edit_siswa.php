<?php
// guru/edit_siswa.php - Edit data siswa oleh wali kelas

include '../includes/header.php';

// Fungsi untuk resize image
function resizeImage($source, $destination, $max_width, $max_height) {
    $info = getimagesize($source);
    if (!$info) return false;

    $width = $info[0];
    $height = $info[1];
    $type = $info[2];

    // Hitung dimensi baru
    $ratio = min($max_width / $width, $max_height / $height);
    $new_width = round($width * $ratio);
    $new_height = round($height * $ratio);

    // Buat image resource berdasarkan tipe
    switch ($type) {
        case IMAGETYPE_JPEG:
            $source_image = imagecreatefromjpeg($source);
            break;
        case IMAGETYPE_PNG:
            $source_image = imagecreatefrompng($source);
            break;
        case IMAGETYPE_GIF:
            $source_image = imagecreatefromgif($source);
            break;
        default:
            return false;
    }

    // Buat image baru dengan dimensi yang sudah dihitung
    $new_image = imagecreatetruecolor($new_width, $new_height);

    // Preserve transparency untuk PNG dan GIF
    if ($type == IMAGETYPE_PNG || $type == IMAGETYPE_GIF) {
        imagealphablending($new_image, false);
        imagesavealpha($new_image, true);
        $transparent = imagecolorallocatealpha($new_image, 255, 255, 255, 127);
        imagefilledrectangle($new_image, 0, 0, $new_width, $new_height, $transparent);
    }

    // Resize image
    imagecopyresampled($new_image, $source_image, 0, 0, 0, 0, $new_width, $new_height, $width, $height);

    // Simpan image
    switch ($type) {
        case IMAGETYPE_JPEG:
            imagejpeg($new_image, $destination, 85);
            break;
        case IMAGETYPE_PNG:
            imagepng($new_image, $destination);
            break;
        case IMAGETYPE_GIF:
            imagegif($new_image, $destination);
            break;
    }

    // Bersihkan memory
    imagedestroy($source_image);
    imagedestroy($new_image);

    return true;
}

$id_siswa = $_GET['id'] ?? 0;
$id_kelas = $_GET['kelas'] ?? 0;
$id_tahun = $_GET['tahun'] ?? 0;

if (!$id_siswa) {
    die('<div class="alert alert-danger">ID siswa tidak valid.</div>');
}

// Cek akses - hanya admin dan wali kelas yang bisa edit
if ($_SESSION['role'] !== 'admin' && $_SESSION['role'] !== 'wali_kelas') {
    die('<div class="alert alert-danger">Akses ditolak. Hanya admin dan wali kelas yang dapat mengedit data siswa.</div>');
}

// Jika wali kelas, pastikan dia bisa akses kelas ini
if ($_SESSION['role'] === 'wali_kelas') {
    requireKelasAccess($conn, $id_kelas, $id_tahun);
}

// Get data siswa
$stmt = $conn->prepare("
    SELECT s.*, k.nama_kelas, tp.tahun_ajaran 
    FROM siswa s 
    JOIN kelas k ON s.id_kelas = k.id_kelas 
    JOIN tahun_pelajaran tp ON s.id_tahun = tp.id_tahun 
    WHERE s.id_siswa = ?
");
$stmt->bind_param("i", $id_siswa);
$stmt->execute();
$siswa = $stmt->get_result()->fetch_assoc();

if (!$siswa) {
    die('<div class="alert alert-danger">Data siswa tidak ditemukan.</div>');
}

$success = '';
$error = '';

// Handle form submission
if ($_POST) {
    $nama_lengkap = trim($_POST['nama_lengkap']);
    $nama_panggilan = trim($_POST['nama_panggilan']);
    $asal_paroki = trim($_POST['asal_paroki']);
    $keuskupan = trim($_POST['keuskupan']);
    $status_ppdb = $_POST['status_ppdb'];
    $gambaran_umum = trim($_POST['gambaran_umum']);
    $kondisi_saat_ini = trim($_POST['kondisi_saat_ini']);
    $catatan = trim($_POST['catatan']);
    $pendampingan = trim($_POST['pendampingan']);
    $hasil_pendampingan = trim($_POST['hasil_pendampingan']);
    $nilai_rata_rata = !empty($_POST['nilai_rata_rata']) ? floatval($_POST['nilai_rata_rata']) : null;
    $is_potensial = isset($_POST['is_potensial']) ? 1 : 0;
    $is_catatan_khusus = isset($_POST['is_catatan_khusus']) ? 1 : 0;

    // Handle hapus foto
    if (isset($_POST['hapus_foto']) && $_POST['hapus_foto']) {
        if ($siswa['foto'] && $siswa['foto'] !== 'placeholder.jpg' && file_exists('../uploads/' . $siswa['foto'])) {
            unlink('../uploads/' . $siswa['foto']);
        }
        $foto_name = 'placeholder.jpg';
    }

    // Handle upload foto
    $foto_name = $siswa['foto']; // Default tetap foto lama

    if (isset($_FILES['foto']) && $_FILES['foto']['error'] == 0) {
        $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        $max_size = 2 * 1024 * 1024; // 2MB

        $file_type = $_FILES['foto']['type'];
        $file_size = $_FILES['foto']['size'];
        $file_tmp = $_FILES['foto']['tmp_name'];

        if (!in_array($file_type, $allowed_types)) {
            $error = "Format file tidak didukung. Gunakan JPG, PNG, atau GIF.";
        } elseif ($file_size > $max_size) {
            $error = "Ukuran file terlalu besar. Maksimal 2MB.";
        } else {
            // Generate nama file unik
            $file_extension = pathinfo($_FILES['foto']['name'], PATHINFO_EXTENSION);
            $foto_name = 'siswa_' . $id_siswa . '_' . time() . '.' . $file_extension;
            $upload_path = '../uploads/' . $foto_name;

            // Hapus foto lama jika bukan placeholder
            if ($siswa['foto'] && $siswa['foto'] !== 'placeholder.jpg' && file_exists('../uploads/' . $siswa['foto'])) {
                unlink('../uploads/' . $siswa['foto']);
            }

            // Upload file baru
            if (move_uploaded_file($file_tmp, $upload_path)) {
                // Resize foto jika terlalu besar
                resizeImage($upload_path, $upload_path, 400, 400);
            } else {
                $error = "Gagal mengupload foto.";
                $foto_name = $siswa['foto']; // Kembalikan ke foto lama
            }
        }
    }

    if (empty($nama_lengkap)) {
        $error = "Nama lengkap harus diisi.";
    } else {
        // Update data siswa (tanpa mengubah kelas dan tahun untuk wali kelas)
        if ($_SESSION['role'] === 'admin') {
            // Admin bisa mengubah kelas dan tahun
            $new_id_kelas = $_POST['id_kelas'];
            $new_id_tahun = $_POST['id_tahun'];
            
            $stmt = $conn->prepare("
                UPDATE siswa SET
                    nama_lengkap = ?, nama_panggilan = ?, asal_paroki = ?, keuskupan = ?, status_ppdb = ?,
                    gambaran_umum = ?, kondisi_saat_ini = ?, catatan = ?, pendampingan = ?, hasil_pendampingan = ?,
                    nilai_rata_rata = ?, id_kelas = ?, id_tahun = ?, is_potensial = ?, is_catatan_khusus = ?, foto = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id_siswa = ?
            ");

            $stmt->bind_param("ssssssssssdiiiisi",
                $nama_lengkap, $nama_panggilan, $asal_paroki, $keuskupan, $status_ppdb,
                $gambaran_umum, $kondisi_saat_ini, $catatan, $pendampingan, $hasil_pendampingan,
                $nilai_rata_rata, $new_id_kelas, $new_id_tahun, $is_potensial, $is_catatan_khusus, $foto_name, $id_siswa
            );
        } else {
            // Wali kelas tidak bisa mengubah kelas dan tahun
            $stmt = $conn->prepare("
                UPDATE siswa SET
                    nama_lengkap = ?, nama_panggilan = ?, asal_paroki = ?, keuskupan = ?, status_ppdb = ?,
                    gambaran_umum = ?, kondisi_saat_ini = ?, catatan = ?, pendampingan = ?, hasil_pendampingan = ?,
                    nilai_rata_rata = ?, is_potensial = ?, is_catatan_khusus = ?, foto = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id_siswa = ?
            ");

            $stmt->bind_param("ssssssssssdiisi",
                $nama_lengkap, $nama_panggilan, $asal_paroki, $keuskupan, $status_ppdb,
                $gambaran_umum, $kondisi_saat_ini, $catatan, $pendampingan, $hasil_pendampingan,
                $nilai_rata_rata, $is_potensial, $is_catatan_khusus, $foto_name, $id_siswa
            );
        }

        if ($stmt->execute()) {
            $success = "Data siswa berhasil diupdate.";
            
            // Refresh data siswa
            $stmt = $conn->prepare("
                SELECT s.*, k.nama_kelas, tp.tahun_ajaran 
                FROM siswa s 
                JOIN kelas k ON s.id_kelas = k.id_kelas 
                JOIN tahun_pelajaran tp ON s.id_tahun = tp.id_tahun 
                WHERE s.id_siswa = ?
            ");
            $stmt->bind_param("i", $id_siswa);
            $stmt->execute();
            $siswa = $stmt->get_result()->fetch_assoc();
        } else {
            $error = "Gagal mengupdate data siswa: " . $conn->error;
        }
    }
}

// Get semua kelas untuk dropdown (hanya untuk admin)
if ($_SESSION['role'] === 'admin') {
    $stmt = $conn->prepare("SELECT * FROM kelas ORDER BY nama_kelas");
    $stmt->execute();
    $kelas_list = $stmt->get_result();

    $stmt = $conn->prepare("SELECT * FROM tahun_pelajaran ORDER BY tahun_ajaran DESC");
    $stmt->execute();
    $tahun_list = $stmt->get_result();
}
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h3><i class="fas fa-user-edit"></i> Edit Siswa</h3>
        <p class="text-muted mb-0">
            Kelas: <?= htmlspecialchars($siswa['nama_kelas']) ?> | 
            Tahun: <?= htmlspecialchars($siswa['tahun_ajaran']) ?>
        </p>
    </div>
    <div>
        <a href="data_siswa.php?kelas=<?= $id_kelas ?: $siswa['id_kelas'] ?>&tahun=<?= $id_tahun ?: $siswa['id_tahun'] ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
        </a>
    </div>
</div>

<?php if ($success): ?>
<div class="alert alert-success alert-dismissible fade show">
    <i class="fas fa-check-circle"></i> <?= $success ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if ($error): ?>
<div class="alert alert-danger alert-dismissible fade show">
    <i class="fas fa-exclamation-circle"></i> <?= $error ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<form method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
    <div class="row">
        <!-- Kolom Kiri -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-user"></i> Data Pribadi</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">Nama Lengkap <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="nama_lengkap" 
                               value="<?= htmlspecialchars($siswa['nama_lengkap']) ?>" required>
                        <div class="invalid-feedback">Nama lengkap harus diisi.</div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Nama Panggilan</label>
                        <input type="text" class="form-control" name="nama_panggilan" 
                               value="<?= htmlspecialchars($siswa['nama_panggilan']) ?>">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Asal Paroki</label>
                        <input type="text" class="form-control" name="asal_paroki" 
                               value="<?= htmlspecialchars($siswa['asal_paroki']) ?>">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Keuskupan</label>
                        <input type="text" class="form-control" name="keuskupan" 
                               value="<?= htmlspecialchars($siswa['keuskupan']) ?>">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Status PPDB</label>
                        <select class="form-select" name="status_ppdb">
                            <option value="Pantas" <?= $siswa['status_ppdb'] == 'Pantas' ? 'selected' : '' ?>>Pantas</option>
                            <option value="Diterima" <?= $siswa['status_ppdb'] == 'Diterima' ? 'selected' : '' ?>>Diterima</option>
                            <option value="Dicoba" <?= $siswa['status_ppdb'] == 'Dicoba' ? 'selected' : '' ?>>Dicoba</option>
                        </select>
                    </div>

                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" name="is_potensial" id="is_potensial"
                               <?= $siswa['is_potensial'] ? 'checked' : '' ?>>
                        <label class="form-check-label" for="is_potensial">
                            <i class="fas fa-star text-warning"></i> Siswa Potensial
                        </label>
                    </div>

                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="is_catatan_khusus" id="is_catatan_khusus"
                               <?= $siswa['is_catatan_khusus'] ? 'checked' : '' ?>>
                        <label class="form-check-label" for="is_catatan_khusus">
                            <i class="fas fa-exclamation-triangle text-warning"></i> Catatan Khusus
                        </label>
                    </div>
                </div>
            </div>

            <!-- Upload Foto -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5><i class="fas fa-camera"></i> Foto Siswa</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <img src="../uploads/<?= htmlspecialchars($siswa['foto'] ?: 'placeholder.jpg') ?>"
                             alt="Foto <?= htmlspecialchars($siswa['nama_lengkap']) ?>"
                             class="img-thumbnail" id="preview-foto"
                             style="width: 150px; height: 150px; object-fit: cover;"
                             onerror="this.src='../uploads/placeholder.jpg'">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Upload Foto Baru</label>
                        <input type="file" class="form-control" name="foto" id="foto"
                               accept="image/jpeg,image/jpg,image/png,image/gif"
                               onchange="previewImage(this)">
                        <div class="form-text">
                            <i class="fas fa-info-circle"></i>
                            Format: JPG, PNG, GIF. Maksimal 2MB. Foto akan otomatis diresize.
                        </div>
                    </div>

                    <?php if ($siswa['foto'] && $siswa['foto'] !== 'placeholder.jpg'): ?>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="hapus_foto" id="hapus_foto">
                        <label class="form-check-label text-danger" for="hapus_foto">
                            <i class="fas fa-trash"></i> Hapus foto yang ada
                        </label>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Pengaturan Kelas (hanya untuk admin) -->
            <?php if ($_SESSION['role'] === 'admin'): ?>
            <div class="card mt-3">
                <div class="card-header">
                    <h5><i class="fas fa-cog"></i> Pengaturan Kelas</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">Kelas</label>
                        <select class="form-select" name="id_kelas">
                            <?php while ($kelas = $kelas_list->fetch_assoc()): ?>
                            <option value="<?= $kelas['id_kelas'] ?>" 
                                    <?= $kelas['id_kelas'] == $siswa['id_kelas'] ? 'selected' : '' ?>>
                                <?= htmlspecialchars($kelas['nama_kelas']) ?> - <?= htmlspecialchars($kelas['tingkat']) ?>
                            </option>
                            <?php endwhile; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Tahun Pelajaran</label>
                        <select class="form-select" name="id_tahun">
                            <?php while ($tahun = $tahun_list->fetch_assoc()): ?>
                            <option value="<?= $tahun['id_tahun'] ?>" 
                                    <?= $tahun['id_tahun'] == $siswa['id_tahun'] ? 'selected' : '' ?>>
                                <?= htmlspecialchars($tahun['tahun_ajaran']) ?>
                            </option>
                            <?php endwhile; ?>
                        </select>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <!-- Kolom Kanan -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-clipboard"></i> Data Pendampingan</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">Gambaran Umum</label>
                        <textarea class="form-control" name="gambaran_umum" rows="3"><?= htmlspecialchars($siswa['gambaran_umum']) ?></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Kondisi Saat Ini</label>
                        <textarea class="form-control" name="kondisi_saat_ini" rows="3"><?= htmlspecialchars($siswa['kondisi_saat_ini']) ?></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Catatan</label>
                        <textarea class="form-control" name="catatan" rows="3"><?= htmlspecialchars($siswa['catatan']) ?></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Pendampingan</label>
                        <textarea class="form-control" name="pendampingan" rows="3"><?= htmlspecialchars($siswa['pendampingan']) ?></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Hasil Pendampingan</label>
                        <textarea class="form-control" name="hasil_pendampingan" rows="3"><?= htmlspecialchars($siswa['hasil_pendampingan']) ?></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Nilai Rata-rata <small class="text-muted">(0-100)</small></label>
                        <input type="number" class="form-control" name="nilai_rata_rata"
                               value="<?= $siswa['nilai_rata_rata'] ? number_format($siswa['nilai_rata_rata'], 1) : '' ?>"
                               min="0" max="100" step="0.1" placeholder="Contoh: 85.5">
                        <small class="text-muted">Kosongkan jika belum ada nilai</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-3">
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-save"></i> Simpan Perubahan
                    </button>
                    <a href="data_siswa.php?kelas=<?= $id_kelas ?: $siswa['id_kelas'] ?>&tahun=<?= $id_tahun ?: $siswa['id_tahun'] ?>" class="btn btn-secondary btn-lg ms-2">
                        <i class="fas fa-times"></i> Batal
                    </a>
                </div>
            </div>
        </div>
    </div>
</form>

<script>
// Preview foto sebelum upload
function previewImage(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('preview-foto').src = e.target.result;
        };
        reader.readAsDataURL(input.files[0]);
    }
}

// Handle checkbox hapus foto
document.getElementById('hapus_foto')?.addEventListener('change', function() {
    if (this.checked) {
        document.getElementById('preview-foto').src = '../uploads/placeholder.jpg';
        document.getElementById('foto').value = '';
    }
});

// Bootstrap form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

<?php include '../includes/footer.php'; ?>
