<?php
// guru/gambaran_kelas.php

// Include header yang sudah menghandle autentikasi
include '../includes/header.php';

$id_kelas = $_GET['kelas'] ?? $_SESSION['current_kelas'] ?? null;
$id_tahun = $_GET['tahun'] ?? $_SESSION['current_tahun'] ?? null;

// Validasi parameter dan akses
if (!$id_kelas || !$id_tahun) {
    die('<div class="alert alert-danger">Parameter kelas dan tahun harus diisi.</div>');
}

// Cek apakah user dapat mengakses kelas ini
if (isset($_SESSION['id_user'])) {
    requireKelasAccess($conn, $id_kelas, $id_tahun);
} else {
    die('<div class="alert alert-danger">Session tidak valid. Silakan login ulang.</div>');
}

// Handle form submission untuk update gambaran kelas
$success = '';
$error = '';

if ($_POST && ($_SESSION['role'] === 'admin' || $_SESSION['role'] === 'wali_kelas')) {
    $hasil_komitmen = trim($_POST['hasil_komitmen_bersama']);
    $aspek_sanitas = trim($_POST['aspek_sanitas']);
    $aspek_sactitas = trim($_POST['aspek_sactitas']);
    $aspek_scientia = trim($_POST['aspek_scientia']);
    $mapel_agama = trim($_POST['mapel_agama']);
    $mapel_bahasa_indonesia = trim($_POST['mapel_bahasa_indonesia']);
    $mapel_bahasa_inggris = trim($_POST['mapel_bahasa_inggris']);
    $mapel_bahasa_latin = trim($_POST['mapel_bahasa_latin']);
    $mapel_lain = trim($_POST['mapel_lain']);

    // Cek apakah kolom updated_at ada
    $check_column = $conn->query("SHOW COLUMNS FROM kelas LIKE 'updated_at'");
    $has_updated_at = $check_column->num_rows > 0;

    if ($has_updated_at) {
        $stmt = $conn->prepare("
            UPDATE kelas SET
                hasil_komitmen_bersama = ?,
                aspek_sanitas = ?,
                aspek_sactitas = ?,
                aspek_scientia = ?,
                mapel_agama = ?,
                mapel_bahasa_indonesia = ?,
                mapel_bahasa_inggris = ?,
                mapel_bahasa_latin = ?,
                mapel_lain = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE id_kelas = ?
        ");
    } else {
        $stmt = $conn->prepare("
            UPDATE kelas SET
                hasil_komitmen_bersama = ?,
                aspek_sanitas = ?,
                aspek_sactitas = ?,
                aspek_scientia = ?,
                mapel_agama = ?,
                mapel_bahasa_indonesia = ?,
                mapel_bahasa_inggris = ?,
                mapel_bahasa_latin = ?,
                mapel_lain = ?
            WHERE id_kelas = ?
        ");
    }

    if ($stmt === false) {
        $error = "Error preparing statement: " . $conn->error;
    } else {
        $stmt->bind_param("sssssssssi",
            $hasil_komitmen, $aspek_sanitas, $aspek_sactitas, $aspek_scientia,
            $mapel_agama, $mapel_bahasa_indonesia, $mapel_bahasa_inggris, $mapel_bahasa_latin, $mapel_lain,
            $id_kelas
        );

        if ($stmt->execute()) {
            $success = "Data gambaran kelas berhasil " . ($has_gambaran_data ? "diupdate" : "disimpan") . ".";
            // Redirect ke mode view setelah save
            header("Location: ?kelas=$id_kelas&tahun=$id_tahun&saved=1");
            exit();
        } else {
            $error = "Gagal mengupdate data: " . $conn->error;
        }
    }
}

// Ambil data kelas lengkap
$stmt = $conn->prepare("SELECT * FROM kelas WHERE id_kelas = ?");
$stmt->bind_param("i", $id_kelas);
$stmt->execute();
$kelas = $stmt->get_result()->fetch_assoc();

// Cek apakah data gambaran kelas sudah ada
$has_gambaran_data = !empty($kelas['hasil_komitmen_bersama']) ||
                     !empty($kelas['aspek_sanitas']) ||
                     !empty($kelas['aspek_sactitas']) ||
                     !empty($kelas['aspek_scientia']) ||
                     !empty($kelas['mapel_agama']) ||
                     !empty($kelas['mapel_bahasa_indonesia']) ||
                     !empty($kelas['mapel_bahasa_inggris']) ||
                     !empty($kelas['mapel_bahasa_latin']) ||
                     !empty($kelas['mapel_lain']);

// Mode edit (default false, true jika ada parameter edit=1)
$edit_mode = isset($_GET['edit']) && $_GET['edit'] == '1';

// Success message jika baru saja disimpan
$just_saved = isset($_GET['saved']) && $_GET['saved'] == '1';

// Get tahun pelajaran
$stmt = $conn->prepare("SELECT tahun_ajaran FROM tahun_pelajaran WHERE id_tahun = ?");
$stmt->bind_param("i", $id_tahun);
$stmt->execute();
$tahun = $stmt->get_result()->fetch_assoc();

// Jumlah siswa
$stmt = $conn->prepare("SELECT COUNT(*) as total FROM siswa WHERE id_kelas = ? AND id_tahun = ?");
$stmt->bind_param("ii", $id_kelas, $id_tahun);
$stmt->execute();
$total = $stmt->get_result()->fetch_assoc();

// Asal Keuskupan
$stmt = $conn->prepare("SELECT keuskupan, COUNT(*) as jml FROM siswa WHERE id_kelas = ? AND id_tahun = ? AND keuskupan != '' GROUP BY keuskupan ORDER BY jml DESC");
$stmt->bind_param("ii", $id_kelas, $id_tahun);
$stmt->execute();
$keuskupan = $stmt->get_result();

// Status PPDB
$stmt = $conn->prepare("SELECT status_ppdb, COUNT(*) as jml FROM siswa WHERE id_kelas = ? AND id_tahun = ? GROUP BY status_ppdb ORDER BY jml DESC");
$stmt->bind_param("ii", $id_kelas, $id_tahun);
$stmt->execute();
$ppdb = $stmt->get_result();

// Siswa Potensial
$stmt = $conn->prepare("SELECT COUNT(*) as total FROM siswa WHERE id_kelas = ? AND id_tahun = ? AND is_potensial = 1");
$stmt->bind_param("ii", $id_kelas, $id_tahun);
$stmt->execute();
$siswa_potensial = $stmt->get_result()->fetch_assoc();

// Siswa Catatan Khusus
$stmt = $conn->prepare("SELECT COUNT(*) as total FROM siswa WHERE id_kelas = ? AND id_tahun = ? AND is_catatan_khusus = 1");
$stmt->bind_param("ii", $id_kelas, $id_tahun);
$stmt->execute();
$siswa_catatan_khusus = $stmt->get_result()->fetch_assoc();

// Data Akademik
// Rata-rata nilai kelas
$stmt = $conn->prepare("SELECT AVG(nilai_rata_rata) as rata_rata_kelas FROM siswa WHERE id_kelas = ? AND id_tahun = ? AND nilai_rata_rata IS NOT NULL");
$stmt->bind_param("ii", $id_kelas, $id_tahun);
$stmt->execute();
$akademik_rata = $stmt->get_result()->fetch_assoc();

// Nilai tertinggi
$stmt = $conn->prepare("SELECT MAX(nilai_rata_rata) as nilai_tertinggi FROM siswa WHERE id_kelas = ? AND id_tahun = ? AND nilai_rata_rata IS NOT NULL");
$stmt->bind_param("ii", $id_kelas, $id_tahun);
$stmt->execute();
$akademik_tertinggi = $stmt->get_result()->fetch_assoc();

// Nilai terendah
$stmt = $conn->prepare("SELECT MIN(nilai_rata_rata) as nilai_terendah FROM siswa WHERE id_kelas = ? AND id_tahun = ? AND nilai_rata_rata IS NOT NULL");
$stmt->bind_param("ii", $id_kelas, $id_tahun);
$stmt->execute();
$akademik_terendah = $stmt->get_result()->fetch_assoc();

// Ranking 5 terbawah
$stmt = $conn->prepare("SELECT nama_lengkap, nilai_rata_rata FROM siswa WHERE id_kelas = ? AND id_tahun = ? AND nilai_rata_rata IS NOT NULL ORDER BY nilai_rata_rata ASC LIMIT 5");
$stmt->bind_param("ii", $id_kelas, $id_tahun);
$stmt->execute();
$ranking_terbawah = $stmt->get_result();

// Jumlah siswa yang memiliki nilai
$stmt = $conn->prepare("SELECT COUNT(*) as total FROM siswa WHERE id_kelas = ? AND id_tahun = ? AND nilai_rata_rata IS NOT NULL");
$stmt->bind_param("ii", $id_kelas, $id_tahun);
$stmt->execute();
$siswa_dengan_nilai = $stmt->get_result()->fetch_assoc();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h3><i class="fas fa-chart-line"></i> Gambaran Kelas: <?= htmlspecialchars($kelas['nama_kelas']) ?></h3>
        <p class="text-muted mb-0">Tahun Pelajaran: <?= htmlspecialchars($tahun['tahun_ajaran']) ?></p>
    </div>
    <div>
        <?php if ($_SESSION['role'] === 'admin'): ?>
        <a href="../admin/view_all_kelas.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
        </a>
        <?php endif; ?>
        <a href="data_siswa.php?kelas=<?= $id_kelas ?>&tahun=<?= $id_tahun ?>" class="btn btn-primary">
            <i class="fas fa-users"></i> Lihat Daftar Siswa
        </a>
    </div>
</div>

<?php if ($success): ?>
<div class="alert alert-success alert-dismissible fade show">
    <i class="fas fa-check-circle"></i> <?= $success ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if ($error): ?>
<div class="alert alert-danger alert-dismissible fade show">
    <i class="fas fa-exclamation-circle"></i> <?= $error ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- Statistik Ringkas -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h4><?= $total['total'] ?></h4>
                <p class="mb-0"><i class="fas fa-users"></i> Total Siswa</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h4><?= $siswa_potensial['total'] ?></h4>
                <p class="mb-0"><i class="fas fa-star"></i> Siswa Potensial</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h4><?= $siswa_catatan_khusus['total'] ?></h4>
                <p class="mb-0"><i class="fas fa-exclamation-triangle"></i> Catatan Khusus</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <h4><?= $keuskupan->num_rows ?></h4>
                <p class="mb-0"><i class="fas fa-church"></i> Asal Keuskupan</p>
            </div>
        </div>
    </div>
</div>

<!-- Data Demografi -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-church"></i> Asal Keuskupan</h5>
            </div>
            <div class="card-body">
                <?php if ($keuskupan->num_rows > 0): ?>
                    <?php
                    $keuskupan->data_seek(0);
                    while($row = $keuskupan->fetch_assoc()):
                    ?>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span><?= htmlspecialchars($row['keuskupan'] ?: 'Tidak diisi') ?></span>
                            <span class="badge bg-primary"><?= $row['jml'] ?> siswa</span>
                        </div>
                    <?php endwhile; ?>
                <?php else: ?>
                    <p class="text-muted">Belum ada data keuskupan</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-clipboard-check"></i> Status PPDB</h5>
            </div>
            <div class="card-body">
                <?php if ($ppdb->num_rows > 0): ?>
                    <?php
                    $ppdb->data_seek(0);
                    while($row = $ppdb->fetch_assoc()):
                        $badge_class = '';
                        switch($row['status_ppdb']) {
                            case 'Pantas': $badge_class = 'bg-success'; break;
                            case 'Diterima': $badge_class = 'bg-primary'; break;
                            case 'Dicoba': $badge_class = 'bg-warning'; break;
                            default: $badge_class = 'bg-secondary';
                        }
                    ?>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span><?= htmlspecialchars($row['status_ppdb']) ?></span>
                            <span class="badge <?= $badge_class ?>"><?= $row['jml'] ?> siswa</span>
                        </div>
                    <?php endwhile; ?>
                <?php else: ?>
                    <p class="text-muted">Belum ada data status PPDB</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Kondisi Akademik -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-gradient-primary text-white">
                <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Kondisi Akademik</h5>
            </div>
            <div class="card-body">
                <?php if ($siswa_dengan_nilai['total'] > 0): ?>
                    <div class="row">
                        <!-- Statistik Nilai -->
                        <div class="col-md-8">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="text-center p-3 border rounded bg-light">
                                        <h4 class="text-primary mb-1">
                                            <?= $akademik_rata['rata_rata_kelas'] ? number_format($akademik_rata['rata_rata_kelas'], 1) : '-' ?>
                                        </h4>
                                        <small class="text-muted">Rata-rata Nilai Siswa</small>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="text-center p-3 border rounded bg-light">
                                        <h4 class="text-success mb-1">
                                            <?= $akademik_tertinggi['nilai_tertinggi'] ? number_format($akademik_tertinggi['nilai_tertinggi'], 1) : '-' ?>
                                        </h4>
                                        <small class="text-muted">Nilai Tertinggi</small>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="text-center p-3 border rounded bg-light">
                                        <h4 class="text-danger mb-1">
                                            <?= $akademik_terendah['nilai_terendah'] ? number_format($akademik_terendah['nilai_terendah'], 1) : '-' ?>
                                        </h4>
                                        <small class="text-muted">Nilai Terendah</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Ranking 5 Terbawah -->
                        <div class="col-md-4">
                            <div class="border rounded p-3 bg-light">
                                <h6 class="text-warning mb-3"><i class="fas fa-exclamation-triangle me-1"></i>Ranking 5 Terbawah</h6>
                                <?php if ($ranking_terbawah->num_rows > 0): ?>
                                    <?php
                                    $ranking_terbawah->data_seek(0);
                                    $no = 1;
                                    while($row = $ranking_terbawah->fetch_assoc()):
                                    ?>
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span class="small"><?= $no ?>. <?= htmlspecialchars($row['nama_lengkap']) ?></span>
                                            <span class="badge bg-warning text-dark"><?= number_format($row['nilai_rata_rata'], 1) ?></span>
                                        </div>
                                    <?php
                                    $no++;
                                    endwhile;
                                    ?>
                                <?php else: ?>
                                    <p class="text-muted small mb-0">Tidak ada data</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            Data berdasarkan <?= $siswa_dengan_nilai['total'] ?> dari <?= $total['total'] ?> siswa yang memiliki nilai.
                        </small>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Belum Ada Data Nilai</h5>
                        <p class="text-muted">Data nilai siswa belum diinput. Silakan hubungi admin untuk menginput data nilai.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Form Gambaran Kelas -->
<?php if ($_SESSION['role'] === 'admin' || $_SESSION['role'] === 'wali_kelas'): ?>

<!-- Success Message -->
<?php if ($just_saved): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-check-circle me-2"></i>
    <strong>Berhasil!</strong> Data gambaran kelas telah disimpan.
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- Toggle Edit Mode Button -->
<?php if ($has_gambaran_data && !$edit_mode): ?>
<div class="card border-0 shadow-lg mb-4" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
    <div class="card-body p-4">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h5 class="mb-2 text-success fw-bold">
                    <i class="fas fa-check-circle me-2"></i>Data Gambaran Kelas Tersimpan
                </h5>
                <p class="text-muted mb-0">
                    Data gambaran kelas sudah tersimpan dan dapat dilihat di bawah.
                    <br><small>Terakhir diupdate: <?= isset($kelas['updated_at']) ? date('d M Y H:i', strtotime($kelas['updated_at'])) : 'Tidak diketahui' ?></small>
                </p>
            </div>
            <div class="d-flex gap-2">
                <button onclick="window.print()" class="btn btn-outline-primary btn-lg rounded-pill">
                    <i class="fas fa-print me-2"></i>Print
                </button>
                <a href="?kelas=<?= $id_kelas ?>&tahun=<?= $id_tahun ?>&edit=1" class="btn btn-warning btn-lg rounded-pill shadow-sm">
                    <i class="fas fa-edit me-2"></i>Edit Gambaran Kelas
                </a>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Form Edit/Input -->
<?php if (!$has_gambaran_data || $edit_mode): ?>
<div class="card border-0 shadow-lg mb-4">
    <div class="card-header bg-gradient-primary-to-secondary text-white">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-clipboard-list me-2"></i>
                <?= $has_gambaran_data ? 'Edit' : 'Input' ?> Gambaran Kelas
            </h5>
            <?php if ($edit_mode): ?>
            <a href="?kelas=<?= $id_kelas ?>&tahun=<?= $id_tahun ?>" class="btn btn-light btn-sm">
                <i class="fas fa-times me-1"></i>Batal
            </a>
            <?php endif; ?>
        </div>
    </div>
    <div class="card-body">
        <form method="POST" class="needs-validation" novalidate>
    <div class="row">
        <!-- Kolom Kiri -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-clipboard-list"></i> Hasil & Aspek Kelas</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">Hasil Komitmen Bersama</label>
                        <textarea class="form-control" name="hasil_komitmen_bersama" rows="4"
                                  placeholder="Catatan hasil komitmen bersama kelas..."><?= htmlspecialchars($kelas['hasil_komitmen_bersama'] ?? '') ?></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Aspek Sanitas</label>
                        <textarea class="form-control" name="aspek_sanitas" rows="3"
                                  placeholder="Catatan aspek sanitas (kesehatan, kebersihan)..."><?= htmlspecialchars($kelas['aspek_sanitas'] ?? '') ?></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Aspek Sactitas</label>
                        <textarea class="form-control" name="aspek_sactitas" rows="3"
                                  placeholder="Catatan aspek sactitas (kesucian, moral)..."><?= htmlspecialchars($kelas['aspek_sactitas'] ?? '') ?></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Aspek Scientia</label>
                        <textarea class="form-control" name="aspek_scientia" rows="3"
                                  placeholder="Catatan aspek scientia (pengetahuan, akademik)..."><?= htmlspecialchars($kelas['aspek_scientia'] ?? '') ?></textarea>
                    </div>
                </div>
            </div>
        </div>

        <!-- Kolom Kanan -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-book"></i> Catatan Kenaikan - Mata Pelajaran</h5>
                </div>
                <div class="card-body">
                    <h6 class="text-primary">A. Mapel Prasyarat</h6>

                    <div class="mb-3">
                        <label class="form-label">1. Agama</label>
                        <textarea class="form-control" name="mapel_agama" rows="2"
                                  placeholder="Nama siswa yang lemah, nilai yang didapat..."><?= htmlspecialchars($kelas['mapel_agama'] ?? '') ?></textarea>
                        <small class="text-muted">Contoh: Budi (65), Sari (70)</small>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">2. Bahasa Indonesia</label>
                        <textarea class="form-control" name="mapel_bahasa_indonesia" rows="2"
                                  placeholder="Nama siswa yang lemah, nilai yang didapat..."><?= htmlspecialchars($kelas['mapel_bahasa_indonesia'] ?? '') ?></textarea>
                        <small class="text-muted">Contoh: Anton (68), Lisa (72)</small>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">3. Bahasa Inggris</label>
                        <textarea class="form-control" name="mapel_bahasa_inggris" rows="2"
                                  placeholder="Nama siswa yang lemah, nilai yang didapat..."><?= htmlspecialchars($kelas['mapel_bahasa_inggris'] ?? '') ?></textarea>
                        <small class="text-muted">Contoh: Rina (60), Doni (65)</small>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">4. Bahasa Latin</label>
                        <textarea class="form-control" name="mapel_bahasa_latin" rows="2"
                                  placeholder="Nama siswa yang lemah, nilai yang didapat..."><?= htmlspecialchars($kelas['mapel_bahasa_latin'] ?? '') ?></textarea>
                        <small class="text-muted">Contoh: Maya (55), Eko (62)</small>
                    </div>

                    <h6 class="text-primary mt-4">B. Mapel Lain</h6>
                    <div class="mb-3">
                        <textarea class="form-control" name="mapel_lain" rows="3"
                                  placeholder="Mata pelajaran lain yang perlu perhatian khusus..."><?= htmlspecialchars($kelas['mapel_lain'] ?? '') ?></textarea>
                        <small class="text-muted">Contoh: Matematika: Tono (58), Fisika: Ani (62)</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-3">
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-save"></i>
                        <?= $has_gambaran_data ? 'Update' : 'Simpan' ?> Gambaran Kelas
                    </button>
                    <button type="reset" class="btn btn-secondary btn-lg ms-2">
                        <i class="fas fa-undo"></i> Reset Form
                    </button>
                    <?php if ($edit_mode): ?>
                    <a href="?kelas=<?= $id_kelas ?>&tahun=<?= $id_tahun ?>" class="btn btn-outline-secondary btn-lg ms-2">
                        <i class="fas fa-times"></i> Batal
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
        </form>
    </div>
</div>
<?php endif; ?>

<!-- Display Mode - Show Saved Data -->
<?php if ($has_gambaran_data && !$edit_mode): ?>
<div class="row">
    <div class="col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-light">
                <h5 class="mb-0"><i class="fas fa-clipboard-list me-2 text-primary"></i>Hasil & Aspek Kelas</h5>
            </div>
            <div class="card-body">
                <?php if (!empty($kelas['hasil_komitmen_bersama'])): ?>
                <div class="mb-3">
                    <h6 class="text-primary">Hasil Komitmen Bersama</h6>
                    <p class="text-muted"><?= nl2br(htmlspecialchars($kelas['hasil_komitmen_bersama'])) ?></p>
                </div>
                <?php endif; ?>

                <?php if (!empty($kelas['aspek_sanitas'])): ?>
                <div class="mb-3">
                    <h6 class="text-primary">Aspek Sanitas</h6>
                    <p class="text-muted"><?= nl2br(htmlspecialchars($kelas['aspek_sanitas'])) ?></p>
                </div>
                <?php endif; ?>

                <?php if (!empty($kelas['aspek_sactitas'])): ?>
                <div class="mb-3">
                    <h6 class="text-primary">Aspek Sactitas</h6>
                    <p class="text-muted"><?= nl2br(htmlspecialchars($kelas['aspek_sactitas'])) ?></p>
                </div>
                <?php endif; ?>

                <?php if (!empty($kelas['aspek_scientia'])): ?>
                <div class="mb-3">
                    <h6 class="text-primary">Aspek Scientia</h6>
                    <p class="text-muted"><?= nl2br(htmlspecialchars($kelas['aspek_scientia'])) ?></p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-light">
                <h5 class="mb-0"><i class="fas fa-book me-2 text-primary"></i>Mata Pelajaran</h5>
            </div>
            <div class="card-body">
                <?php if (!empty($kelas['mapel_agama'])): ?>
                <div class="mb-3">
                    <h6 class="text-primary">Agama</h6>
                    <p class="text-muted"><?= nl2br(htmlspecialchars($kelas['mapel_agama'])) ?></p>
                </div>
                <?php endif; ?>

                <?php if (!empty($kelas['mapel_bahasa_indonesia'])): ?>
                <div class="mb-3">
                    <h6 class="text-primary">Bahasa Indonesia</h6>
                    <p class="text-muted"><?= nl2br(htmlspecialchars($kelas['mapel_bahasa_indonesia'])) ?></p>
                </div>
                <?php endif; ?>

                <?php if (!empty($kelas['mapel_bahasa_inggris'])): ?>
                <div class="mb-3">
                    <h6 class="text-primary">Bahasa Inggris</h6>
                    <p class="text-muted"><?= nl2br(htmlspecialchars($kelas['mapel_bahasa_inggris'])) ?></p>
                </div>
                <?php endif; ?>

                <?php if (!empty($kelas['mapel_bahasa_latin'])): ?>
                <div class="mb-3">
                    <h6 class="text-primary">Bahasa Latin</h6>
                    <p class="text-muted"><?= nl2br(htmlspecialchars($kelas['mapel_bahasa_latin'])) ?></p>
                </div>
                <?php endif; ?>

                <?php if (!empty($kelas['mapel_lain'])): ?>
                <div class="mb-3">
                    <h6 class="text-primary">Mata Pelajaran Lain</h6>
                    <p class="text-muted"><?= nl2br(htmlspecialchars($kelas['mapel_lain'])) ?></p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<?php endif; ?>

<script>
// Bootstrap form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

<?php include '../includes/footer.php'; ?>